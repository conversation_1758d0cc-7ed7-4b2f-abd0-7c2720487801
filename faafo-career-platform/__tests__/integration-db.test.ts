/**
 * Integration Database Test
 * Clean test without any module mocking interference
 */

// Import Prisma and bcrypt normally
import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcrypt';

describe('Integration Database Tests', () => {
  let prisma: any;

  beforeAll(async () => {
    // Create fresh Prisma client instance
    prisma = new PrismaClient({
      datasources: {
        db: {
          url: process.env.DATABASE_URL,
        },
      },
      log: ['error'],
    });
    
    await prisma.$connect();
    console.log('✅ Integration test database connected');
  });

  afterAll(async () => {
    if (prisma) {
      await prisma.$disconnect();
      console.log('🔌 Integration test database disconnected');
    }
  });

  beforeEach(async () => {
    // Clean up any test data before each test
    try {
      await prisma.user.deleteMany({
        where: {
          email: {
            contains: 'integrationtest',
          },
        },
      });
    } catch (error) {
      // Ignore cleanup errors
    }
  });

  it('should perform basic database operations', async () => {
    // Test 1: Simple query
    const result = await prisma.$queryRaw`SELECT 1 as test`;
    expect(Array.isArray(result)).toBe(true);
    expect(result[0]).toHaveProperty('test', 1);

    // Test 2: Count operation
    const userCount = await prisma.user.count();
    expect(typeof userCount).toBe('number');
    expect(userCount).toBeGreaterThanOrEqual(0);

    // Test 3: Find operation
    const careerPaths = await prisma.careerPath.findMany({
      take: 5,
      select: {
        id: true,
        name: true,
        slug: true,
        isActive: true,
      },
    });
    
    expect(Array.isArray(careerPaths)).toBe(true);
    console.log(`Found ${careerPaths.length} career paths`);
  });

  it('should create and manage users', async () => {
    const hashedPassword = await bcrypt.hash('testpassword123', 10);
    const uniqueEmail = `integrationtest-${Date.now()}@example.com`;

    // Create user
    const user = await prisma.user.create({
      data: {
        email: uniqueEmail,
        password: hashedPassword,
        name: 'Integration Test User',
      },
    });

    expect(user).toBeDefined();
    expect(user.id).toBeDefined();
    expect(user.email).toBe(uniqueEmail);
    expect(user.name).toBe('Integration Test User');
    expect(user.createdAt).toBeInstanceOf(Date);

    console.log('✅ User created:', {
      id: user.id,
      email: user.email,
      name: user.name,
    });

    // Find user
    const foundUser = await prisma.user.findUnique({
      where: { email: uniqueEmail },
    });

    expect(foundUser).toBeDefined();
    expect(foundUser.id).toBe(user.id);

    // Update user
    const updatedUser = await prisma.user.update({
      where: { id: user.id },
      data: { name: 'Updated Integration Test User' },
    });

    expect(updatedUser.name).toBe('Updated Integration Test User');

    // Delete user
    await prisma.user.delete({
      where: { id: user.id },
    });

    // Verify deletion
    const deletedUser = await prisma.user.findUnique({
      where: { email: uniqueEmail },
    });

    expect(deletedUser).toBeNull();
    console.log('✅ User lifecycle test completed');
  });

  it('should handle learning resources', async () => {
    const resourceCount = await prisma.learningResource.count();
    expect(typeof resourceCount).toBe('number');

    const resources = await prisma.learningResource.findMany({
      take: 3,
      select: {
        id: true,
        title: true,
        category: true,
        skillLevel: true,
        isActive: true,
      },
    });

    expect(Array.isArray(resources)).toBe(true);
    console.log(`Found ${resources.length} learning resources`);
  });

  it('should handle assessments', async () => {
    // Create a test user first
    const hashedPassword = await bcrypt.hash('testpassword123', 10);
    const uniqueEmail = `assessmenttest-${Date.now()}@example.com`;

    const user = await prisma.user.create({
      data: {
        email: uniqueEmail,
        password: hashedPassword,
        name: 'Assessment Test User',
      },
    });

    // Create an assessment
    const assessment = await prisma.assessment.create({
      data: {
        userId: user.id,
        status: 'IN_PROGRESS',
        currentStep: 1,
      },
    });

    expect(assessment).toBeDefined();
    expect(assessment.userId).toBe(user.id);
    expect(assessment.status).toBe('IN_PROGRESS');

    // Add assessment responses
    const response = await prisma.assessmentResponse.create({
      data: {
        assessmentId: assessment.id,
        questionKey: 'test_question',
        answerValue: { answer: 'test_answer' },
      },
    });

    expect(response).toBeDefined();
    expect(response.assessmentId).toBe(assessment.id);

    // Clean up
    await prisma.assessmentResponse.deleteMany({
      where: { assessmentId: assessment.id },
    });
    
    await prisma.assessment.delete({
      where: { id: assessment.id },
    });
    
    await prisma.user.delete({
      where: { id: user.id },
    });

    console.log('✅ Assessment test completed');
  });
});
