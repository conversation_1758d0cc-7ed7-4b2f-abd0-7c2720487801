/**
 * Optimized AI Service with Advanced Request Deduplication
 * 
 * Integrates advanced deduplication, intelligent caching, and performance monitoring
 * for optimal AI service call efficiency in Phase 2 implementation.
 */

import { geminiService } from './services/geminiService';
import { advancedRequestDeduplication } from './advanced-request-deduplication';
import { enhancedCacheService } from './services/enhanced-cache-service';
import { AIServiceLogger } from './services/geminiService';

interface OptimizedAIConfig {
  enableAdvancedDeduplication: boolean;
  enableIntelligentCaching: boolean;
  enablePerformanceMonitoring: boolean;
  enableRequestBatching: boolean;
  maxConcurrentRequests: number;
  defaultTimeout: number;
  enableFallbacks: boolean;
}

interface AIRequestOptions {
  userId?: string;
  priority?: 'low' | 'medium' | 'high';
  timeout?: number;
  enableDeduplication?: boolean;
  enableCaching?: boolean;
  cacheKey?: string;
  cacheTTL?: number;
  enableSemanticMatch?: boolean;
  enableCrossUserMatch?: boolean;
}

interface OptimizedAIResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  metadata: {
    source: 'cache' | 'deduplication' | 'ai' | 'fallback';
    responseTime: number;
    deduplicationSavings?: number;
    cacheHit?: boolean;
    requestId: string;
    timestamp: string;
  };
}

export class OptimizedAIService {
  private config: OptimizedAIConfig;
  private activeRequests: Map<string, Promise<any>>;
  private requestCounter: number;

  constructor(config?: Partial<OptimizedAIConfig>) {
    this.config = {
      enableAdvancedDeduplication: true,
      enableIntelligentCaching: true,
      enablePerformanceMonitoring: true,
      enableRequestBatching: true,
      maxConcurrentRequests: 10,
      defaultTimeout: 30000,
      enableFallbacks: true,
      ...config
    };

    this.activeRequests = new Map();
    this.requestCounter = 0;

    AIServiceLogger.info('Optimized AI Service initialized', {
      config: this.config
    });
  }

  /**
   * Optimized skills analysis with advanced deduplication
   */
  async analyzeSkillsGap(
    currentSkills: any[],
    targetCareerPath: string,
    experienceLevel: string,
    options: AIRequestOptions = {}
  ): Promise<OptimizedAIResponse<any>> {
    const requestId = this.generateRequestId();
    const startTime = Date.now();

    try {
      // Generate request key for deduplication
      const requestKey = `skills-analysis:${targetCareerPath}:${experienceLevel}:${JSON.stringify(currentSkills)}`;
      
      // Check cache first if enabled
      if (this.config.enableIntelligentCaching && options.enableCaching !== false) {
        const cacheKey = options.cacheKey || `skills_gap_${targetCareerPath}_${experienceLevel}`;
        const cached = await enhancedCacheService.get(cacheKey);
        if (cached) {
          return {
            success: true,
            data: cached,
            metadata: {
              source: 'cache',
              responseTime: Date.now() - startTime,
              cacheHit: true,
              requestId,
              timestamp: new Date().toISOString()
            }
          };
        }
      }

      // Use advanced deduplication if enabled
      if (this.config.enableAdvancedDeduplication && options.enableDeduplication !== false) {
        const result = await advancedRequestDeduplication.deduplicateRequest(
          requestKey,
          () => geminiService.analyzeSkillsGap(currentSkills, targetCareerPath, experienceLevel, options.userId),
          {
            userId: options.userId,
            priority: this.mapPriority(options.priority),
            timeout: options.timeout || this.config.defaultTimeout,
            enableSemanticMatch: options.enableSemanticMatch,
            enableCrossUserMatch: options.enableCrossUserMatch
          }
        );

        // Cache the result if successful
        if (result.success && this.config.enableIntelligentCaching) {
          const cacheKey = options.cacheKey || `skills_gap_${targetCareerPath}_${experienceLevel}`;
          await enhancedCacheService.set(cacheKey, result.data, options.cacheTTL || 1800);
        }

        return {
          success: result.success,
          data: result.data,
          error: result.error,
          metadata: {
            source: 'deduplication',
            responseTime: Date.now() - startTime,
            requestId,
            timestamp: new Date().toISOString()
          }
        };
      }

      // Fallback to direct AI service call
      const result = await geminiService.analyzeSkillsGap(
        currentSkills, 
        targetCareerPath, 
        experienceLevel, 
        options.userId
      );

      return {
        success: result.success,
        data: result.data,
        error: result.error,
        metadata: {
          source: 'ai',
          responseTime: Date.now() - startTime,
          requestId,
          timestamp: new Date().toISOString()
        }
      };

    } catch (error) {
      AIServiceLogger.error('Optimized skills analysis failed', {
        requestId,
        error: error instanceof Error ? error.message : String(error)
      });

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        metadata: {
          source: 'ai',
          responseTime: Date.now() - startTime,
          requestId,
          timestamp: new Date().toISOString()
        }
      };
    }
  }

  /**
   * Optimized career recommendations with deduplication
   */
  async generateCareerRecommendations(
    skills: string[],
    preferences: any,
    options: AIRequestOptions = {}
  ): Promise<OptimizedAIResponse<any>> {
    const requestId = this.generateRequestId();
    const startTime = Date.now();

    try {
      const requestKey = `career-recommendations:${JSON.stringify(skills)}:${JSON.stringify(preferences)}`;
      
      // Check cache first
      if (this.config.enableIntelligentCaching && options.enableCaching !== false) {
        const cacheKey = options.cacheKey || `career_rec_${this.hashObject({ skills, preferences })}`;
        const cached = await enhancedCacheService.get(cacheKey);
        if (cached) {
          return {
            success: true,
            data: cached,
            metadata: {
              source: 'cache',
              responseTime: Date.now() - startTime,
              cacheHit: true,
              requestId,
              timestamp: new Date().toISOString()
            }
          };
        }
      }

      // Use advanced deduplication
      if (this.config.enableAdvancedDeduplication && options.enableDeduplication !== false) {
        const result = await advancedRequestDeduplication.deduplicateRequest(
          requestKey,
          () => geminiService.generateCareerRecommendations(skills, preferences, options.userId),
          {
            userId: options.userId,
            priority: this.mapPriority(options.priority),
            timeout: options.timeout || this.config.defaultTimeout,
            enableSemanticMatch: options.enableSemanticMatch,
            enableCrossUserMatch: options.enableCrossUserMatch
          }
        );

        // Cache successful results
        if (result.success && this.config.enableIntelligentCaching) {
          const cacheKey = options.cacheKey || `career_rec_${this.hashObject({ skills, preferences })}`;
          await enhancedCacheService.set(cacheKey, result.data, options.cacheTTL || 3600);
        }

        return {
          success: result.success,
          data: result.data,
          error: result.error,
          metadata: {
            source: 'deduplication',
            responseTime: Date.now() - startTime,
            requestId,
            timestamp: new Date().toISOString()
          }
        };
      }

      // Direct AI service call
      const result = await geminiService.generateCareerRecommendations(skills, preferences, options.userId);

      return {
        success: result.success,
        data: result.data,
        error: result.error,
        metadata: {
          source: 'ai',
          responseTime: Date.now() - startTime,
          requestId,
          timestamp: new Date().toISOString()
        }
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        metadata: {
          source: 'ai',
          responseTime: Date.now() - startTime,
          requestId,
          timestamp: new Date().toISOString()
        }
      };
    }
  }

  /**
   * Optimized interview question generation
   */
  async generateInterviewQuestions(
    sessionConfig: any,
    options: AIRequestOptions = {}
  ): Promise<OptimizedAIResponse<any>> {
    const requestId = this.generateRequestId();
    const startTime = Date.now();

    try {
      const requestKey = `interview-questions:${JSON.stringify(sessionConfig)}`;
      
      // Use advanced deduplication for interview questions
      if (this.config.enableAdvancedDeduplication && options.enableDeduplication !== false) {
        const result = await advancedRequestDeduplication.deduplicateRequest(
          requestKey,
          () => geminiService.generateInterviewQuestions(sessionConfig),
          {
            userId: options.userId,
            priority: this.mapPriority(options.priority),
            timeout: options.timeout || this.config.defaultTimeout,
            enableSemanticMatch: options.enableSemanticMatch !== false, // Default to true for questions
            enableCrossUserMatch: options.enableCrossUserMatch !== false // Safe for cross-user sharing
          }
        );

        return {
          success: result.success,
          data: result.data,
          error: result.error,
          metadata: {
            source: 'deduplication',
            responseTime: Date.now() - startTime,
            requestId,
            timestamp: new Date().toISOString()
          }
        };
      }

      // Direct AI service call
      const result = await geminiService.generateInterviewQuestions(sessionConfig);

      return {
        success: result.success,
        data: result.data,
        error: result.error,
        metadata: {
          source: 'ai',
          responseTime: Date.now() - startTime,
          requestId,
          timestamp: new Date().toISOString()
        }
      };

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        metadata: {
          source: 'ai',
          responseTime: Date.now() - startTime,
          requestId,
          timestamp: new Date().toISOString()
        }
      };
    }
  }

  /**
   * Get optimization metrics and performance statistics
   */
  getOptimizationMetrics() {
    const deduplicationMetrics = advancedRequestDeduplication.getMetrics();
    
    return {
      deduplication: deduplicationMetrics,
      activeRequests: this.activeRequests.size,
      totalRequests: this.requestCounter,
      config: this.config
    };
  }

  private generateRequestId(): string {
    return `opt_ai_${++this.requestCounter}_${Date.now()}`;
  }

  private mapPriority(priority?: 'low' | 'medium' | 'high'): number {
    switch (priority) {
      case 'high': return 3;
      case 'medium': return 2;
      case 'low': return 1;
      default: return 2;
    }
  }

  private hashObject(obj: any): string {
    return Buffer.from(JSON.stringify(obj)).toString('base64').substring(0, 16);
  }
}

// Export singleton instance
export const optimizedAIService = new OptimizedAIService();
