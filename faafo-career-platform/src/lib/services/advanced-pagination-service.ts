/**
 * Advanced Pagination Service
 * Provides both offset-based and cursor-based pagination for optimal performance
 * Part of Phase 2B: Advanced Query Optimization
 */

import { Prisma } from '@prisma/client';
import { cacheService } from './cacheService';
import { enhancedCacheKeyGenerator } from '../enhancedCacheKeyGenerator';

export interface PaginationOptions {
  page?: number;
  limit?: number;
  cursor?: string;
  direction?: 'forward' | 'backward';
  orderBy?: Record<string, 'asc' | 'desc'>;
  maxLimit?: number;
}

export interface PaginationResult<T> {
  data: T[];
  pagination: {
    page?: number;
    limit: number;
    total?: number;
    totalPages?: number;
    hasNext: boolean;
    hasPrev: boolean;
    nextCursor?: string;
    prevCursor?: string;
    cursors?: {
      first?: string;
      last?: string;
    };
  };
  performance: {
    queryTime: number;
    cacheHit: boolean;
    optimizationType: 'offset' | 'cursor';
  };
}

export interface CursorPaginationConfig {
  cursorField: string;
  orderDirection: 'asc' | 'desc';
  uniqueField?: string;
}

class AdvancedPaginationService {
  private readonly DEFAULT_LIMIT = 10;
  private readonly MAX_LIMIT = 100;
  private readonly CACHE_TTL = 300; // 5 minutes

  /**
   * Determine optimal pagination strategy based on dataset size and query patterns
   */
  private async determineOptimalStrategy(
    model: string,
    whereClause: any,
    options: PaginationOptions
  ): Promise<'offset' | 'cursor'> {
    // Use cursor pagination for:
    // 1. Large datasets (>10k records)
    // 2. Deep pagination (page > 100)
    // 3. When cursor is provided
    
    if (options.cursor) return 'cursor';
    if (options.page && options.page > 100) return 'cursor';

    // Quick count check for dataset size
    const cacheKey = enhancedCacheKeyGenerator.generateKey('pagination', 'strategy', {
      model,
      where: JSON.stringify(whereClause)
    });

    let totalCount = await cacheService.getJSON(cacheKey) as number;
    
    if (totalCount === null) {
      // This is a simplified count - in production, you'd use the actual Prisma model
      totalCount = 0; // Placeholder - would be replaced with actual count
      await cacheService.setJSON(cacheKey, totalCount, 3600); // Cache for 1 hour
    }

    return totalCount > 10000 ? 'cursor' : 'offset';
  }

  /**
   * Execute offset-based pagination (traditional page/limit)
   */
  async executeOffsetPagination<T>(
    queryFn: (skip: number, take: number) => Promise<T[]>,
    countFn: () => Promise<number>,
    options: PaginationOptions,
    cacheKey?: string
  ): Promise<PaginationResult<T>> {
    const startTime = Date.now();
    const page = Math.max(1, options.page || 1);
    const limit = Math.min(options.limit || this.DEFAULT_LIMIT, options.maxLimit || this.MAX_LIMIT);
    const skip = (page - 1) * limit;

    let cacheHit = false;
    let data: T[];
    let total: number;

    // Check cache if key provided
    if (cacheKey) {
      const cached = await cacheService.getJSON(cacheKey);
      if (cached && typeof cached === 'object' && 'data' in cached) {
        return cached as PaginationResult<T>;
      }
    }

    // Execute queries in parallel
    [data, total] = await Promise.all([
      queryFn(skip, limit),
      countFn()
    ]);

    const totalPages = Math.ceil(total / limit);
    const hasNext = page < totalPages;
    const hasPrev = page > 1;

    const result: PaginationResult<T> = {
      data,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext,
        hasPrev
      },
      performance: {
        queryTime: Date.now() - startTime,
        cacheHit,
        optimizationType: 'offset'
      }
    };

    // Cache result if key provided
    if (cacheKey) {
      await cacheService.setJSON(cacheKey, result, this.CACHE_TTL);
    }

    return result;
  }

  /**
   * Execute cursor-based pagination (for large datasets)
   */
  async executeCursorPagination<T>(
    queryFn: (cursor?: any, take?: number, direction?: 'forward' | 'backward') => Promise<T[]>,
    options: PaginationOptions,
    config: CursorPaginationConfig,
    cacheKey?: string
  ): Promise<PaginationResult<T>> {
    const startTime = Date.now();
    const limit = Math.min(options.limit || this.DEFAULT_LIMIT, options.maxLimit || this.MAX_LIMIT);
    const direction = options.direction || 'forward';

    let cacheHit = false;
    let data: T[];

    // Check cache if key provided
    if (cacheKey) {
      const cached = await cacheService.getJSON(cacheKey);
      if (cached && typeof cached === 'object' && 'data' in cached) {
        return cached as PaginationResult<T>;
      }
    }

    // Execute cursor query
    data = await queryFn(options.cursor, limit + 1, direction); // Get one extra to check hasNext

    const hasNext = data.length > limit;
    const hasPrev = !!options.cursor;

    // Remove the extra item if present
    if (hasNext) {
      data = data.slice(0, limit);
    }

    // Generate cursors
    const cursors = this.generateCursors(data, config.cursorField);

    const result: PaginationResult<T> = {
      data,
      pagination: {
        limit,
        hasNext,
        hasPrev,
        nextCursor: hasNext ? cursors.last : undefined,
        prevCursor: hasPrev ? cursors.first : undefined,
        cursors
      },
      performance: {
        queryTime: Date.now() - startTime,
        cacheHit,
        optimizationType: 'cursor'
      }
    };

    // Cache result if key provided
    if (cacheKey) {
      await cacheService.setJSON(cacheKey, result, this.CACHE_TTL);
    }

    return result;
  }

  /**
   * Generate cursor values from data
   */
  private generateCursors<T>(data: T[], cursorField: string): { first?: string; last?: string } {
    if (data.length === 0) return {};

    const first = data[0];
    const last = data[data.length - 1];

    return {
      first: this.encodeCursor((first as any)[cursorField]),
      last: this.encodeCursor((last as any)[cursorField])
    };
  }

  /**
   * Encode cursor value for URL safety
   */
  private encodeCursor(value: any): string {
    return Buffer.from(JSON.stringify(value)).toString('base64');
  }

  /**
   * Decode cursor value
   */
  decodeCursor(cursor: string): any {
    try {
      return JSON.parse(Buffer.from(cursor, 'base64').toString());
    } catch {
      throw new Error('Invalid cursor format');
    }
  }

  /**
   * Smart pagination - automatically chooses optimal strategy
   */
  async smartPaginate<T>(
    model: string,
    queryFn: {
      offset: (skip: number, take: number) => Promise<T[]>;
      cursor: (cursor?: any, take?: number, direction?: 'forward' | 'backward') => Promise<T[]>;
      count: () => Promise<number>;
    },
    whereClause: any,
    options: PaginationOptions,
    cursorConfig?: CursorPaginationConfig,
    cacheKey?: string
  ): Promise<PaginationResult<T>> {
    const strategy = await this.determineOptimalStrategy(model, whereClause, options);

    if (strategy === 'cursor' && cursorConfig) {
      return this.executeCursorPagination(queryFn.cursor, options, cursorConfig, cacheKey);
    } else {
      return this.executeOffsetPagination(queryFn.offset, queryFn.count, options, cacheKey);
    }
  }

  /**
   * Create standardized pagination response
   */
  createPaginationResponse<T>(result: PaginationResult<T>, message?: string) {
    return {
      success: true,
      ...(message && { message }),
      data: result.data,
      pagination: result.pagination,
      performance: result.performance
    };
  }

  /**
   * Validate pagination parameters
   */
  validatePaginationParams(options: PaginationOptions): PaginationOptions {
    const validated: PaginationOptions = { ...options };

    if (validated.page !== undefined) {
      validated.page = Math.max(1, Math.floor(validated.page));
    }

    if (validated.limit !== undefined) {
      validated.limit = Math.min(
        Math.max(1, Math.floor(validated.limit)),
        validated.maxLimit || this.MAX_LIMIT
      );
    }

    return validated;
  }
}

export const advancedPaginationService = new AdvancedPaginationService();
export default advancedPaginationService;
