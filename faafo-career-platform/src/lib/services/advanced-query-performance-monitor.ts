/**
 * Advanced Query Performance Monitor
 * Provides detailed query performance tracking, automated optimization suggestions, and regression detection
 * Part of Phase 2B: Advanced Query Optimization
 */

import { cacheService } from './cacheService';
import { enhancedCacheKeyGenerator } from '../enhancedCacheKeyGenerator';

export interface QueryPerformanceMetric {
  id: string;
  queryName: string;
  executionTime: number;
  timestamp: Date;
  parameters?: Record<string, any>;
  resultCount?: number;
  cacheHit: boolean;
  optimizationType?: 'offset' | 'cursor' | 'select' | 'index';
  complexity: 'low' | 'medium' | 'high';
  recommendations: string[];
  metadata?: {
    userId?: string;
    endpoint?: string;
    userAgent?: string;
    ipAddress?: string;
  };
}

export interface PerformanceThreshold {
  queryName: string;
  warningThreshold: number; // ms
  criticalThreshold: number; // ms
  maxResultCount?: number;
  enabled: boolean;
}

export interface PerformanceAlert {
  id: string;
  queryName: string;
  severity: 'warning' | 'critical';
  message: string;
  timestamp: Date;
  metric: QueryPerformanceMetric;
  acknowledged: boolean;
}

export interface PerformanceReport {
  period: {
    start: Date;
    end: Date;
  };
  summary: {
    totalQueries: number;
    averageExecutionTime: number;
    slowestQuery: QueryPerformanceMetric;
    fastestQuery: QueryPerformanceMetric;
    cacheHitRate: number;
    alertCount: number;
  };
  trends: {
    queryName: string;
    averageTime: number;
    trend: 'improving' | 'degrading' | 'stable';
    changePercent: number;
  }[];
  recommendations: string[];
}

class AdvancedQueryPerformanceMonitor {
  private metrics: QueryPerformanceMetric[] = [];
  private alerts: PerformanceAlert[] = [];
  private thresholds: Map<string, PerformanceThreshold> = new Map();
  private readonly MAX_METRICS = 10000; // Keep last 10k metrics
  private readonly MAX_ALERTS = 1000; // Keep last 1k alerts

  constructor() {
    this.initializeDefaultThresholds();
  }

  /**
   * Track a query performance metric
   */
  async trackQuery(
    queryName: string,
    executionTime: number,
    options: {
      parameters?: Record<string, any>;
      resultCount?: number;
      cacheHit?: boolean;
      optimizationType?: 'offset' | 'cursor' | 'select' | 'index';
      metadata?: QueryPerformanceMetric['metadata'];
    } = {}
  ): Promise<void> {
    const metric: QueryPerformanceMetric = {
      id: this.generateMetricId(),
      queryName,
      executionTime,
      timestamp: new Date(),
      parameters: options.parameters,
      resultCount: options.resultCount,
      cacheHit: options.cacheHit || false,
      optimizationType: options.optimizationType,
      complexity: this.calculateComplexity(executionTime, options.resultCount),
      recommendations: this.generateRecommendations(queryName, executionTime, options),
      metadata: options.metadata
    };

    // Add to metrics array
    this.metrics.push(metric);
    
    // Maintain size limit
    if (this.metrics.length > this.MAX_METRICS) {
      this.metrics = this.metrics.slice(-this.MAX_METRICS);
    }

    // Check for threshold violations
    await this.checkThresholds(metric);

    // Cache recent metrics for API access
    await this.cacheRecentMetrics();
  }

  /**
   * Set performance threshold for a query
   */
  setThreshold(threshold: PerformanceThreshold): void {
    this.thresholds.set(threshold.queryName, threshold);
  }

  /**
   * Get performance metrics for a specific query
   */
  getQueryMetrics(
    queryName: string,
    limit: number = 100,
    startDate?: Date,
    endDate?: Date
  ): QueryPerformanceMetric[] {
    let filtered = this.metrics.filter(m => m.queryName === queryName);

    if (startDate) {
      filtered = filtered.filter(m => m.timestamp >= startDate);
    }

    if (endDate) {
      filtered = filtered.filter(m => m.timestamp <= endDate);
    }

    return filtered.slice(-limit);
  }

  /**
   * Get performance report for a time period
   */
  generatePerformanceReport(
    startDate: Date,
    endDate: Date,
    queryNames?: string[]
  ): PerformanceReport {
    let filteredMetrics = this.metrics.filter(
      m => m.timestamp >= startDate && m.timestamp <= endDate
    );

    if (queryNames && queryNames.length > 0) {
      filteredMetrics = filteredMetrics.filter(m => queryNames.includes(m.queryName));
    }

    const summary = this.calculateSummary(filteredMetrics);
    const trends = this.calculateTrends(filteredMetrics, startDate, endDate);
    const recommendations = this.generateReportRecommendations(filteredMetrics);

    return {
      period: { start: startDate, end: endDate },
      summary,
      trends,
      recommendations
    };
  }

  /**
   * Get active alerts
   */
  getActiveAlerts(): PerformanceAlert[] {
    return this.alerts.filter(a => !a.acknowledged);
  }

  /**
   * Acknowledge an alert
   */
  acknowledgeAlert(alertId: string): boolean {
    const alert = this.alerts.find(a => a.id === alertId);
    if (alert) {
      alert.acknowledged = true;
      return true;
    }
    return false;
  }

  /**
   * Get optimization suggestions for slow queries
   */
  getOptimizationSuggestions(queryName: string): string[] {
    const recentMetrics = this.getQueryMetrics(queryName, 50);
    const avgTime = recentMetrics.reduce((sum, m) => sum + m.executionTime, 0) / recentMetrics.length;
    
    const suggestions: string[] = [];

    if (avgTime > 1000) {
      suggestions.push('Consider adding database indexes for this query');
      suggestions.push('Implement query result caching');
    }

    if (avgTime > 500) {
      suggestions.push('Optimize SELECT statements to fetch only required fields');
      suggestions.push('Consider pagination for large result sets');
    }

    const cacheHitRate = recentMetrics.filter(m => m.cacheHit).length / recentMetrics.length;
    if (cacheHitRate < 0.3) {
      suggestions.push('Improve caching strategy for this query');
    }

    const highResultCountQueries = recentMetrics.filter(m => (m.resultCount || 0) > 100);
    if (highResultCountQueries.length > recentMetrics.length * 0.5) {
      suggestions.push('Implement cursor-based pagination for large datasets');
    }

    return suggestions;
  }

  /**
   * Detect performance regressions
   */
  detectRegressions(queryName: string, lookbackDays: number = 7): {
    hasRegression: boolean;
    details?: {
      currentAverage: number;
      previousAverage: number;
      changePercent: number;
      significance: 'minor' | 'moderate' | 'severe';
    };
  } {
    const now = new Date();
    const currentPeriodStart = new Date(now.getTime() - (lookbackDays * 24 * 60 * 60 * 1000));
    const previousPeriodStart = new Date(currentPeriodStart.getTime() - (lookbackDays * 24 * 60 * 60 * 1000));

    const currentMetrics = this.getQueryMetrics(queryName, 1000, currentPeriodStart, now);
    const previousMetrics = this.getQueryMetrics(queryName, 1000, previousPeriodStart, currentPeriodStart);

    if (currentMetrics.length < 5 || previousMetrics.length < 5) {
      return { hasRegression: false };
    }

    const currentAverage = currentMetrics.reduce((sum, m) => sum + m.executionTime, 0) / currentMetrics.length;
    const previousAverage = previousMetrics.reduce((sum, m) => sum + m.executionTime, 0) / previousMetrics.length;

    const changePercent = ((currentAverage - previousAverage) / previousAverage) * 100;

    if (changePercent > 20) {
      let significance: 'minor' | 'moderate' | 'severe' = 'minor';
      if (changePercent > 50) significance = 'moderate';
      if (changePercent > 100) significance = 'severe';

      return {
        hasRegression: true,
        details: {
          currentAverage,
          previousAverage,
          changePercent,
          significance
        }
      };
    }

    return { hasRegression: false };
  }

  /**
   * Private helper methods
   */
  private initializeDefaultThresholds(): void {
    const defaultThresholds: PerformanceThreshold[] = [
      { queryName: 'careerPath.findFirst', warningThreshold: 500, criticalThreshold: 1000, enabled: true },
      { queryName: 'learningResource.findMany', warningThreshold: 300, criticalThreshold: 800, enabled: true },
      { queryName: 'user.findUnique', warningThreshold: 100, criticalThreshold: 300, enabled: true },
      { queryName: 'skillGapAnalysis.create', warningThreshold: 1000, criticalThreshold: 2000, enabled: true },
      { queryName: 'forumPost.findMany', warningThreshold: 400, criticalThreshold: 1000, enabled: true }
    ];

    defaultThresholds.forEach(threshold => {
      this.thresholds.set(threshold.queryName, threshold);
    });
  }

  private generateMetricId(): string {
    return `metric_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private calculateComplexity(executionTime: number, resultCount?: number): 'low' | 'medium' | 'high' {
    if (executionTime > 1000 || (resultCount && resultCount > 1000)) return 'high';
    if (executionTime > 300 || (resultCount && resultCount > 100)) return 'medium';
    return 'low';
  }

  private generateRecommendations(
    queryName: string,
    executionTime: number,
    options: any
  ): string[] {
    const recommendations: string[] = [];

    if (executionTime > 1000) {
      recommendations.push('Query execution time is high. Consider optimization.');
    }

    if (!options.cacheHit && executionTime > 200) {
      recommendations.push('Consider implementing caching for this query.');
    }

    if (options.resultCount && options.resultCount > 100) {
      recommendations.push('Large result set detected. Consider pagination.');
    }

    return recommendations;
  }

  private async checkThresholds(metric: QueryPerformanceMetric): Promise<void> {
    const threshold = this.thresholds.get(metric.queryName);
    if (!threshold || !threshold.enabled) return;

    let severity: 'warning' | 'critical' | null = null;

    if (metric.executionTime >= threshold.criticalThreshold) {
      severity = 'critical';
    } else if (metric.executionTime >= threshold.warningThreshold) {
      severity = 'warning';
    }

    if (severity) {
      const alert: PerformanceAlert = {
        id: `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        queryName: metric.queryName,
        severity,
        message: `Query execution time (${metric.executionTime}ms) exceeded ${severity} threshold (${severity === 'critical' ? threshold.criticalThreshold : threshold.warningThreshold}ms)`,
        timestamp: new Date(),
        metric,
        acknowledged: false
      };

      this.alerts.push(alert);

      // Maintain size limit
      if (this.alerts.length > this.MAX_ALERTS) {
        this.alerts = this.alerts.slice(-this.MAX_ALERTS);
      }
    }
  }

  private async cacheRecentMetrics(): Promise<void> {
    const recentMetrics = this.metrics.slice(-100);
    const cacheKey = enhancedCacheKeyGenerator.generateKey('performance', 'recent_metrics', {});
    await cacheService.setJSON(cacheKey, recentMetrics, 300); // Cache for 5 minutes
  }

  private calculateSummary(metrics: QueryPerformanceMetric[]): PerformanceReport['summary'] {
    if (metrics.length === 0) {
      return {
        totalQueries: 0,
        averageExecutionTime: 0,
        slowestQuery: {} as QueryPerformanceMetric,
        fastestQuery: {} as QueryPerformanceMetric,
        cacheHitRate: 0,
        alertCount: 0
      };
    }

    const totalTime = metrics.reduce((sum, m) => sum + m.executionTime, 0);
    const cacheHits = metrics.filter(m => m.cacheHit).length;
    const slowest = metrics.reduce((prev, current) => 
      prev.executionTime > current.executionTime ? prev : current
    );
    const fastest = metrics.reduce((prev, current) => 
      prev.executionTime < current.executionTime ? prev : current
    );

    return {
      totalQueries: metrics.length,
      averageExecutionTime: totalTime / metrics.length,
      slowestQuery: slowest,
      fastestQuery: fastest,
      cacheHitRate: cacheHits / metrics.length,
      alertCount: this.getActiveAlerts().length
    };
  }

  private calculateTrends(
    metrics: QueryPerformanceMetric[],
    startDate: Date,
    endDate: Date
  ): PerformanceReport['trends'] {
    const queryGroups = new Map<string, QueryPerformanceMetric[]>();
    
    metrics.forEach(metric => {
      if (!queryGroups.has(metric.queryName)) {
        queryGroups.set(metric.queryName, []);
      }
      queryGroups.get(metric.queryName)!.push(metric);
    });

    return Array.from(queryGroups.entries()).map(([queryName, queryMetrics]) => {
      const averageTime = queryMetrics.reduce((sum, m) => sum + m.executionTime, 0) / queryMetrics.length;
      
      // Simple trend calculation - compare first half vs second half
      const midpoint = Math.floor(queryMetrics.length / 2);
      const firstHalf = queryMetrics.slice(0, midpoint);
      const secondHalf = queryMetrics.slice(midpoint);
      
      const firstHalfAvg = firstHalf.reduce((sum, m) => sum + m.executionTime, 0) / firstHalf.length;
      const secondHalfAvg = secondHalf.reduce((sum, m) => sum + m.executionTime, 0) / secondHalf.length;
      
      const changePercent = ((secondHalfAvg - firstHalfAvg) / firstHalfAvg) * 100;
      
      let trend: 'improving' | 'degrading' | 'stable' = 'stable';
      if (changePercent < -10) trend = 'improving';
      if (changePercent > 10) trend = 'degrading';

      return {
        queryName,
        averageTime,
        trend,
        changePercent
      };
    });
  }

  private generateReportRecommendations(metrics: QueryPerformanceMetric[]): string[] {
    const recommendations: string[] = [];
    
    const slowQueries = metrics.filter(m => m.executionTime > 1000);
    if (slowQueries.length > 0) {
      recommendations.push(`${slowQueries.length} slow queries detected. Consider optimization.`);
    }

    const lowCacheHitRate = metrics.filter(m => m.cacheHit).length / metrics.length;
    if (lowCacheHitRate < 0.3) {
      recommendations.push('Low cache hit rate detected. Review caching strategy.');
    }

    return recommendations;
  }
}

export const advancedQueryPerformanceMonitor = new AdvancedQueryPerformanceMonitor();

/**
 * Prisma middleware for automatic query performance tracking
 */
export function setupAdvancedQueryTracking(prisma: any) {
  prisma.$use(async (params: any, next: any) => {
    const start = Date.now();
    const queryName = `${params.model}.${params.action}`;

    try {
      const result = await next(params);
      const executionTime = Date.now() - start;

      // Determine result count
      let resultCount: number | undefined;
      if (Array.isArray(result)) {
        resultCount = result.length;
      } else if (result && typeof result === 'object') {
        resultCount = 1;
      }

      // Track the query performance
      await advancedQueryPerformanceMonitor.trackQuery(queryName, executionTime, {
        parameters: {
          model: params.model,
          action: params.action,
          args: params.args
        },
        resultCount,
        cacheHit: false, // Prisma queries are not cached by default
        optimizationType: params.action.includes('findMany') ? 'offset' : 'select'
      });

      return result;
    } catch (error) {
      const executionTime = Date.now() - start;

      // Track failed queries too
      await advancedQueryPerformanceMonitor.trackQuery(`${queryName}_ERROR`, executionTime, {
        parameters: {
          model: params.model,
          action: params.action,
          error: error instanceof Error ? error.message : 'Unknown error'
        },
        cacheHit: false
      });

      throw error;
    }
  });
}

export default advancedQueryPerformanceMonitor;
