'use client';

import React, { useState, useEffect } from 'react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  AlertCircle, 
  CheckCircle, 
  Info, 
  XCircle, 
  RefreshCw, 
  Copy,
  ChevronDown,
  ChevronUp
} from 'lucide-react';
import { cn } from '@/lib/utils';

export type FeedbackType = 'success' | 'error' | 'warning' | 'info' | 'loading';

export interface FeedbackMessage {
  id?: string;
  type: FeedbackType;
  title?: string;
  message: string;
  details?: string[];
  actions?: FeedbackAction[];
  dismissible?: boolean;
  autoHide?: boolean;
  duration?: number;
}

export interface FeedbackAction {
  label: string;
  onClick: () => void;
  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link';
  loading?: boolean;
}

const feedbackIcons = {
  success: CheckCircle,
  error: XCircle,
  warning: AlertCircle,
  info: Info,
  loading: RefreshCw
};

const feedbackColors = {
  success: 'text-green-600 dark:text-green-400 border-green-200 dark:border-green-800 bg-green-50 dark:bg-green-950/20',
  error: 'text-destructive border-destructive/20 bg-destructive/10',
  warning: 'text-yellow-600 dark:text-yellow-400 border-yellow-200 dark:border-yellow-800 bg-yellow-50 dark:bg-yellow-950/20',
  info: 'text-muted-foreground border-border bg-muted',
  loading: 'text-muted-foreground border-border bg-muted'
};

interface ErrorFeedbackProps {
  feedback: FeedbackMessage;
  onDismiss?: () => void;
  className?: string;
}

export function ErrorFeedback({ feedback, onDismiss, className }: ErrorFeedbackProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [isVisible, setIsVisible] = useState(true);

  const Icon = feedbackIcons[feedback.type];

  useEffect(() => {
    if (feedback.autoHide && feedback.duration) {
      const timer = setTimeout(() => {
        setIsVisible(false);
        onDismiss?.();
      }, feedback.duration);

      return () => clearTimeout(timer);
    }
  }, [feedback.autoHide, feedback.duration, onDismiss]);

  const handleCopyDetails = () => {
    if (feedback.details) {
      navigator.clipboard.writeText(feedback.details.join('\n'));
    }
  };

  if (!isVisible) return null;

  return (
    <Alert className={cn(feedbackColors[feedback.type], className)}>
      <Icon className={cn(
        'h-4 w-4',
        feedback.type === 'loading' && 'animate-spin'
      )} />
      <div className="flex-1">
        {feedback.title && <AlertTitle>{feedback.title}</AlertTitle>}
        <AlertDescription className="mt-1">
          {feedback.message}
        </AlertDescription>

        {/* Details Section */}
        {feedback.details && feedback.details.length > 0 && (
          <div className="mt-3">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
              className="p-0 h-auto text-sm"
            >
              {isExpanded ? (
                <>
                  <ChevronUp className="h-3 w-3 mr-1" />
                  Hide Details
                </>
              ) : (
                <>
                  <ChevronDown className="h-3 w-3 mr-1" />
                  Show Details
                </>
              )}
            </Button>
            
            {isExpanded && (
              <div className="mt-2 p-3 bg-white/50 rounded border">
                <div className="flex justify-between items-start mb-2">
                  <span className="text-sm font-medium">Error Details:</span>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleCopyDetails}
                    className="h-6 w-6 p-0"
                  >
                    <Copy className="h-3 w-3" />
                  </Button>
                </div>
                <ul className="text-sm space-y-1">
                  {feedback.details.map((detail, index) => (
                    <li key={index} className="text-muted-foreground">
                      • {detail}
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        )}

        {/* Actions Section */}
        {feedback.actions && feedback.actions.length > 0 && (
          <div className="flex gap-2 mt-3">
            {feedback.actions.map((action, index) => (
              <Button
                key={index}
                variant={action.variant || 'default'}
                size="sm"
                onClick={action.onClick}
                disabled={action.loading}
              >
                {action.loading && <RefreshCw className="h-3 w-3 mr-1 animate-spin" />}
                {action.label}
              </Button>
            ))}
          </div>
        )}
      </div>

      {/* Dismiss Button */}
      {feedback.dismissible && (
        <Button
          variant="ghost"
          size="sm"
          onClick={() => {
            setIsVisible(false);
            onDismiss?.();
          }}
          className="h-6 w-6 p-0 ml-2"
        >
          <XCircle className="h-4 w-4" />
        </Button>
      )}
    </Alert>
  );
}

interface FeedbackManagerProps {
  messages: FeedbackMessage[];
  onDismiss: (id: string) => void;
  className?: string;
}

export function FeedbackManager({ messages, onDismiss, className }: FeedbackManagerProps) {
  return (
    <div className={cn('space-y-3', className)}>
      {messages.map((message) => (
        <ErrorFeedback
          key={message.id || Math.random().toString()}
          feedback={message}
          onDismiss={() => onDismiss(message.id || '')}
        />
      ))}
    </div>
  );
}

interface FormErrorDisplayProps {
  errors: Record<string, string>;
  className?: string;
}

export function FormErrorDisplay({ errors, className }: FormErrorDisplayProps) {
  const errorEntries = Object.entries(errors).filter(([_, error]) => error);

  if (errorEntries.length === 0) return null;

  return (
    <Card className={cn('border-red-200 bg-red-50', className)}>
      <CardHeader className="pb-3">
        <CardTitle className="text-red-800 text-sm flex items-center">
          <XCircle className="h-4 w-4 mr-2" />
          Please fix the following errors:
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-0">
        <ul className="space-y-1">
          {errorEntries.map(([field, error]) => (
            <li key={field} className="text-sm text-red-700">
              <strong className="capitalize">{field}:</strong> {error}
            </li>
          ))}
        </ul>
      </CardContent>
    </Card>
  );
}

interface LoadingStateProps {
  isLoading: boolean;
  message?: string;
  children: React.ReactNode;
  className?: string;
}

export function LoadingState({ isLoading, message, children, className }: LoadingStateProps) {
  if (isLoading) {
    return (
      <div className={cn('flex items-center justify-center p-8', className)}>
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-2 text-muted-foreground" />
          <p className="text-muted-foreground">{message || 'Loading...'}</p>
        </div>
      </div>
    );
  }

  return <>{children}</>;
}

interface SuccessStateProps {
  message: string;
  description?: string;
  actions?: FeedbackAction[];
  className?: string;
}

export function SuccessState({ message, description, actions, className }: SuccessStateProps) {
  return (
    <Card className={cn('border-green-200 bg-green-50', className)}>
      <CardContent className="pt-6">
        <div className="text-center">
          <CheckCircle className="h-12 w-12 text-green-600 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-green-800 mb-2">{message}</h3>
          {description && (
            <p className="text-green-700 mb-4">{description}</p>
          )}
          {actions && actions.length > 0 && (
            <div className="flex gap-2 justify-center">
              {actions.map((action, index) => (
                <Button
                  key={index}
                  variant={action.variant || 'default'}
                  onClick={action.onClick}
                  disabled={action.loading}
                >
                  {action.loading && <RefreshCw className="h-4 w-4 mr-2 animate-spin" />}
                  {action.label}
                </Button>
              ))}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
