'use client';

import React, { useState, memo, useMemo, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { getDemandColor, getSeverityColor, getSeverityBgColor } from '@/lib/design-system-colors';
import { 
  TrendingUp, 
  TrendingDown, 
  Target, 
  Clock, 
  BookOpen, 
  Award, 
  AlertTriangle,
  CheckCircle,
  BarChart3,
  Lightbulb,
  Calendar,
  DollarSign
} from 'lucide-react';

interface SkillGap {
  skillId: string;
  skillName: string;
  currentLevel: number;
  targetLevel: number;
  gapSeverity: 'CRITICAL' | 'HIGH' | 'MEDIUM' | 'LOW';
  priority: number;
  estimatedLearningTime: number;
  marketDemand: 'VERY_HIGH' | 'HIGH' | 'MODERATE' | 'LOW' | 'VERY_LOW';
  salaryImpact: number;
}

interface LearningMilestone {
  month: number;
  skills: string[];
  estimatedHours: number;
  learningPaths: string[];
  objectives?: string[];
  assessmentCriteria?: string[];
}

interface LearningResource {
  resourceId: string;
  resourceType: 'COURSE' | 'BOOK' | 'PROJECT' | 'CERTIFICATION' | 'TUTORIAL';
  priority: 'CRITICAL' | 'HIGH' | 'MEDIUM' | 'LOW' | 'OPTIONAL';
  skillsAddressed: string[];
  estimatedHours: number;
  cost?: 'FREE' | 'FREEMIUM' | 'PAID';
  difficulty?: 'BEGINNER' | 'INTERMEDIATE' | 'ADVANCED' | 'EXPERT';
  prerequisites?: string[];
}

interface LearningPlan {
  totalEstimatedHours: number;
  milestones: LearningMilestone[];
  recommendedResources: LearningResource[];
}

interface CareerReadiness {
  currentScore: number;
  targetScore: number;
  improvementPotential: number;
  timeToTarget: number;
  confidenceLevel?: number;
  marketCompetitiveness?: number;
}

interface MarketInsights {
  industryTrends: Array<{
    skill: string;
    trend: 'DECLINING' | 'STABLE' | 'GROWING' | 'RAPIDLY_GROWING' | 'EMERGING';
    demandLevel: 'VERY_HIGH' | 'HIGH' | 'MODERATE' | 'LOW' | 'VERY_LOW';
    futureOutlook?: string;
  }>;
  salaryProjections: {
    currentEstimate: number;
    targetEstimate: number;
    improvementPotential: number;
    timeToRealization?: number;
  };
  competitiveAdvantage?: Array<{
    skill: string;
    advantage: string;
    rarity: 'COMMON' | 'UNCOMMON' | 'RARE' | 'VERY_RARE';
  }>;
}

interface SkillGapAnalysisProps {
  analysisId: string;
  skillGaps: SkillGap[];
  learningPlan: LearningPlan;
  careerReadiness: CareerReadiness;
  marketInsights?: MarketInsights;
  generatedAt: string;
  cached?: boolean;
  edgeCaseHandlerData?: {
    sanitizedInput?: any;
    isNewUser?: boolean;
    onboardingRecommendations?: string[];
    retryCount?: number;
    fallbackDataUsed?: boolean;
    suggestedAlternatives?: string[];
  };
}

const SkillGapAnalysis = memo(function SkillGapAnalysis({
  analysisId,
  skillGaps,
  learningPlan,
  careerReadiness,
  marketInsights,
  generatedAt,
  cached = false,
  edgeCaseHandlerData,
}: SkillGapAnalysisProps) {
  const [selectedTab, setSelectedTab] = useState('overview');

  // Memoize expensive calculations
  // Memoize critical skill gaps count to prevent recalculation on every render
  const criticalSkillGapsCount = useMemo(() => {
    return skillGaps.filter(gap => gap.gapSeverity === 'CRITICAL').length;
  }, [skillGaps]);

  // Memoize sorted skill gaps for consistent rendering
  const sortedSkillGaps = useMemo(() => {
    return [...skillGaps].sort((a, b) => {
      // Sort by priority first, then by severity
      if (a.priority !== b.priority) {
        return a.priority - b.priority;
      }
      const severityOrder = { 'CRITICAL': 0, 'HIGH': 1, 'MEDIUM': 2, 'LOW': 3 };
      return severityOrder[a.gapSeverity] - severityOrder[b.gapSeverity];
    });
  }, [skillGaps]);

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'RAPIDLY_GROWING':
      case 'GROWING':
        return <TrendingUp className="h-4 w-4 text-green-600 dark:text-green-400" />;
      case 'DECLINING':
        return <TrendingDown className="h-4 w-4 text-destructive" />;
      case 'EMERGING':
        return <Lightbulb className="h-4 w-4 text-purple-600 dark:text-purple-400" />;
      default:
        return <BarChart3 className="h-4 w-4 text-muted-foreground" />;
    }
  };

  const formatHours = (hours: number) => {
    if (hours < 1) return `${Math.round(hours * 60)} minutes`;
    if (hours < 24) return `${hours} hours`;
    return `${Math.round(hours / 24)} days`;
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Target className="h-5 w-5" />
                Skill Gap Analysis
              </CardTitle>
              <CardDescription>
                Generated on {new Date(generatedAt).toLocaleDateString()}
                <div className="flex gap-2 mt-1">
                  {cached && <Badge variant="secondary">Cached</Badge>}
                  {edgeCaseHandlerData?.fallbackDataUsed && (
                    <Badge variant="outline" className="text-orange-600 border-orange-600">
                      Fallback Data
                    </Badge>
                  )}
                  {edgeCaseHandlerData?.isNewUser && (
                    <Badge variant="outline" className="text-blue-600 border-blue-600">
                      New User
                    </Badge>
                  )}
                  {edgeCaseHandlerData?.retryCount && edgeCaseHandlerData.retryCount > 0 && (
                    <Badge variant="outline" className="text-yellow-600 border-yellow-600">
                      Retried {edgeCaseHandlerData.retryCount}x
                    </Badge>
                  )}
                </div>
              </CardDescription>
            </div>
            <div className="text-right">
              <div className="text-2xl font-bold text-blue-600">
                {careerReadiness.currentScore}%
              </div>
              <div className="text-sm text-gray-500">Career Readiness</div>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* New User Onboarding Recommendations */}
      {edgeCaseHandlerData?.isNewUser && edgeCaseHandlerData.onboardingRecommendations && (
        <Card className="border-blue-200 bg-blue-50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-blue-700">
              <BookOpen className="h-5 w-5" />
              Welcome! Getting Started Recommendations
            </CardTitle>
            <CardDescription className="text-blue-600">
              Since you're new to our platform, here are some personalized recommendations to help you get started.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ul className="space-y-2">
              {edgeCaseHandlerData.onboardingRecommendations.map((recommendation, index) => (
                <li key={index} className="flex items-start gap-2">
                  <CheckCircle className="h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0" />
                  <span className="text-sm text-blue-700">{recommendation}</span>
                </li>
              ))}
            </ul>
          </CardContent>
        </Card>
      )}

      {/* Suggested Alternatives */}
      {edgeCaseHandlerData?.suggestedAlternatives && edgeCaseHandlerData.suggestedAlternatives.length > 0 && (
        <Card className="border-yellow-200 bg-yellow-50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-yellow-700">
              <AlertTriangle className="h-5 w-5" />
              Alternative Recommendations
            </CardTitle>
            <CardDescription className="text-yellow-600">
              Based on your profile, you might also consider these alternative skills or career paths.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-2">
              {edgeCaseHandlerData.suggestedAlternatives.map((alternative, index) => (
                <Badge key={index} variant="outline" className="text-yellow-700 border-yellow-700">
                  {alternative}
                </Badge>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Tabs */}
      <Tabs value={selectedTab} onValueChange={setSelectedTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="gaps">Skill Gaps</TabsTrigger>
          <TabsTrigger value="plan">Learning Plan</TabsTrigger>
          <TabsTrigger value="market">Market Insights</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          {/* Career Readiness */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CheckCircle className="h-5 w-5" />
                Career Readiness Score
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Current Score</span>
                  <span className="font-medium">{careerReadiness.currentScore}%</span>
                </div>
                <Progress value={careerReadiness.currentScore} className="h-3" />
              </div>
              
              <div className="grid grid-cols-3 gap-4 text-center">
                <div>
                  <div className="text-2xl font-bold text-green-600">
                    {careerReadiness.targetScore}%
                  </div>
                  <div className="text-sm text-gray-500">Target Score</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-blue-600">
                    +{careerReadiness.improvementPotential}%
                  </div>
                  <div className="text-sm text-gray-500">Potential Growth</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-purple-600">
                    {careerReadiness.timeToTarget}mo
                  </div>
                  <div className="text-sm text-gray-500">Time to Target</div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Quick Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-2xl font-bold">{skillGaps.length}</p>
                    <p className="text-sm text-gray-500">Skill Gaps</p>
                  </div>
                  <AlertTriangle className="h-8 w-8 text-orange-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-2xl font-bold">{formatHours(learningPlan.totalEstimatedHours)}</p>
                    <p className="text-sm text-gray-500">Learning Time</p>
                  </div>
                  <Clock className="h-8 w-8 text-blue-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-2xl font-bold">{learningPlan.milestones.length}</p>
                    <p className="text-sm text-gray-500">Milestones</p>
                  </div>
                  <Calendar className="h-8 w-8 text-green-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-2xl font-bold">
                      {marketInsights?.salaryProjections ? 
                        `+${Math.round(marketInsights.salaryProjections.improvementPotential)}%` : 
                        'N/A'
                      }
                    </p>
                    <p className="text-sm text-gray-500">Salary Impact</p>
                  </div>
                  <DollarSign className="h-8 w-8 text-purple-500" />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Critical Gaps Alert */}
          {skillGaps.some(gap => gap.gapSeverity === 'CRITICAL') && (
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                You have {criticalSkillGapsCount} critical skill gaps
                that require immediate attention for your career goals.
              </AlertDescription>
            </Alert>
          )}
        </TabsContent>

        {/* Skill Gaps Tab */}
        <TabsContent value="gaps" className="space-y-4">
          {sortedSkillGaps.map((gap, index) => (
            <Card key={gap.skillId} className="border-l-4" style={{borderLeftColor: getSeverityBgColor(gap.gapSeverity)}}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg">{gap.skillName}</CardTitle>
                  <div className="flex items-center gap-2">
                    <Badge
                      variant="secondary"
                      className={getSeverityColor(gap.gapSeverity)}
                    >
                      {gap.gapSeverity}
                    </Badge>
                    <Badge variant="outline" className={getDemandColor(gap.marketDemand)}>
                      {gap.marketDemand} Demand
                    </Badge>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  <div>
                    <div className="text-gray-500">Current Level</div>
                    <div className="font-medium">{gap.currentLevel}/10</div>
                  </div>
                  <div>
                    <div className="text-gray-500">Target Level</div>
                    <div className="font-medium">{gap.targetLevel}/10</div>
                  </div>
                  <div>
                    <div className="text-gray-500">Priority</div>
                    <div className="font-medium">{gap.priority}/100</div>
                  </div>
                  <div>
                    <div className="text-gray-500">Learning Time</div>
                    <div className="font-medium">{formatHours(gap.estimatedLearningTime)}</div>
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Progress to Target</span>
                    <span>{Math.round((gap.currentLevel / gap.targetLevel) * 100)}%</span>
                  </div>
                  <Progress value={(gap.currentLevel / gap.targetLevel) * 100} className="h-2" />
                </div>

                {gap.salaryImpact > 0 && (
                  <div className="flex items-center gap-2 text-sm text-green-600">
                    <DollarSign className="h-4 w-4" />
                    <span>Potential salary impact: +{gap.salaryImpact}%</span>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </TabsContent>

        {/* Learning Plan Tab */}
        <TabsContent value="plan" className="space-y-6">
          {/* Plan Overview */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BookOpen className="h-5 w-5" />
                Learning Plan Overview
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4 text-center">
                <div>
                  <div className="text-2xl font-bold text-blue-600">
                    {formatHours(learningPlan.totalEstimatedHours)}
                  </div>
                  <div className="text-sm text-gray-500">Total Learning Time</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-green-600">
                    {learningPlan.milestones.length}
                  </div>
                  <div className="text-sm text-gray-500">Learning Milestones</div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Milestones */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Learning Milestones</h3>
            {learningPlan.milestones.map((milestone, index) => (
              <Card key={index}>
                <CardHeader>
                  <CardTitle className="text-base">
                    Month {milestone.month} - {formatHours(milestone.estimatedHours)}
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div>
                    <div className="text-sm font-medium text-gray-700 mb-1">Skills Focus:</div>
                    <div className="flex flex-wrap gap-1">
                      {milestone.skills.map((skill, skillIndex) => (
                        <Badge key={skillIndex} variant="secondary">{skill}</Badge>
                      ))}
                    </div>
                  </div>
                  
                  {milestone.learningPaths.length > 0 && (
                    <div>
                      <div className="text-sm font-medium text-gray-700 mb-1">Learning Paths:</div>
                      <div className="flex flex-wrap gap-1">
                        {milestone.learningPaths.map((path, pathIndex) => (
                          <Badge key={pathIndex} variant="outline">{path}</Badge>
                        ))}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Recommended Resources */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Recommended Resources</h3>
            {learningPlan.recommendedResources.map((resource, index) => (
              <Card key={index}>
                <CardContent className="pt-6">
                  <div className="flex items-start justify-between">
                    <div className="space-y-2">
                      <div className="flex items-center gap-2">
                        <Badge variant="outline">{resource.resourceType}</Badge>
                        <Badge 
                          variant={resource.priority === 'CRITICAL' ? 'destructive' : 'secondary'}
                        >
                          {resource.priority}
                        </Badge>
                        {resource.cost && (
                          <Badge variant="secondary">{resource.cost}</Badge>
                        )}
                      </div>
                      <div className="text-sm text-gray-600">
                        Skills: {resource.skillsAddressed.join(', ')}
                      </div>
                      <div className="text-sm text-gray-500">
                        Estimated time: {formatHours(resource.estimatedHours)}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        {/* Market Insights Tab */}
        <TabsContent value="market" className="space-y-6">
          {marketInsights ? (
            <>
              {/* Salary Projections */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <DollarSign className="h-5 w-5" />
                    Salary Projections
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-3 gap-4 text-center">
                    <div>
                      <div className="text-2xl font-bold">
                        {formatCurrency(marketInsights.salaryProjections.currentEstimate)}
                      </div>
                      <div className="text-sm text-gray-500">Current Estimate</div>
                    </div>
                    <div>
                      <div className="text-2xl font-bold text-green-600">
                        {formatCurrency(marketInsights.salaryProjections.targetEstimate)}
                      </div>
                      <div className="text-sm text-gray-500">Target Estimate</div>
                    </div>
                    <div>
                      <div className="text-2xl font-bold text-blue-600">
                        +{Math.round(marketInsights.salaryProjections.improvementPotential)}%
                      </div>
                      <div className="text-sm text-gray-500">Improvement</div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Industry Trends */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <TrendingUp className="h-5 w-5" />
                    Industry Trends
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {marketInsights.industryTrends.map((trend, index) => (
                      <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center gap-3">
                          {getTrendIcon(trend.trend)}
                          <div>
                            <div className="font-medium">{trend.skill}</div>
                            <div className="text-sm text-gray-500">{trend.trend.replace('_', ' ')}</div>
                          </div>
                        </div>
                        <Badge variant="outline" className={getDemandColor(trend.demandLevel)}>
                          {trend.demandLevel} Demand
                        </Badge>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </>
          ) : (
            <Card>
              <CardContent className="pt-6">
                <div className="text-center text-gray-500">
                  Market insights not available for this analysis
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
});

export default SkillGapAnalysis;
