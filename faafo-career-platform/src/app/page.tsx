"use client";

import React, { useEffect } from "react";
import Link from 'next/link';
import { useSession } from 'next-auth/react';
import { Send, GraduationCap, DollarSign, Lightbulb, MessageSquare, Briefcase, BookOpen } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import SEOHead from '@/components/seo/SEOHead';
import { pageSEOConfigs } from '@/lib/seo/seo-config';

// Force dynamic rendering
export const dynamic = 'force-dynamic';


interface HeroSectionProps {
  title: string;
  subtitle: string;
  primaryAction: {
    text: string;
    href: string;
    icon?: React.ReactNode;
  };
  secondaryAction: {
    text: string;
    href: string;
  };
}

function HeroSection({
  title,
  subtitle,
  primaryAction,
  secondaryAction,
}: HeroSectionProps) {
  return (
    <section className="relative min-h-[calc(100vh-80px)] flex flex-col items-center justify-center bg-background text-foreground pt-20 pb-32 px-4 overflow-hidden text-center">
      <div className="relative z-10 flex flex-col items-center gap-8">
        <h1 className="text-5xl sm:text-6xl md:text-7xl font-bold leading-tight">
          {title}
        </h1>
        <p className="text-lg sm:text-xl md:text-2xl max-w-2xl text-foreground">
          {subtitle}
        </p>
        <div className="flex flex-col sm:flex-row justify-center gap-4 mt-6">
          <Link href={primaryAction.href} className="inline-flex items-center justify-center gap-2 bg-primary hover:bg-primary/90 text-primary-foreground text-lg px-8 py-3 rounded-md font-medium transition-colors">
            {primaryAction.icon && <span>{primaryAction.icon}</span>}
            {primaryAction.text}
          </Link>
          <Link href={secondaryAction.href} className="inline-flex items-center justify-center bg-secondary hover:bg-secondary/80 text-secondary-foreground text-lg px-8 py-3 rounded-md font-medium transition-colors">
            {secondaryAction.text}
          </Link>
        </div>
      </div>
    </section>
  );
}

interface FeatureCardProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  href?: string;
}

function FeatureCard({ icon, title, description, href }: FeatureCardProps) {
  const content = (
    <div className="bg-white dark:bg-gray-800 p-6 rounded-lg text-center flex flex-col items-center border border-gray-200 dark:border-gray-700 hover:shadow-lg transition-shadow">
      <div className="text-gray-600 dark:text-gray-400 mb-4">{icon && <span>{icon}</span>}</div>
      <h3 className="text-xl font-semibold mb-2">{title}</h3>
      <p className="text-gray-600 dark:text-gray-400 text-center">{description}</p>
    </div>
  );

  if (href) {
    return (
      <Link href={href} className="block cursor-pointer hover:scale-105 transition-transform">
        {content}
      </Link>
    );
  }

  return content;
}

export default function Home() {
  const { status } = useSession();

  // Use comprehensive SEO system
  useEffect(() => {
    // Inject structured data for homepage
    const structuredData = [
      {
        '@context': 'https://schema.org',
        '@type': 'WebSite',
        name: 'FAAFO Career Platform',
        url: process.env.NEXT_PUBLIC_BASE_URL || 'https://faafo-career.com',
        description: 'Empowering career transitions through personalized assessments, financial planning, and community support.',
        potentialAction: {
          '@type': 'SearchAction',
          target: `${process.env.NEXT_PUBLIC_BASE_URL || 'https://faafo-career.com'}/search?q={search_term_string}`,
          'query-input': 'required name=search_term_string',
        },
      },
      {
        '@context': 'https://schema.org',
        '@type': 'Organization',
        name: 'FAAFO Career Platform',
        url: process.env.NEXT_PUBLIC_BASE_URL || 'https://faafo-career.com',
        logo: `${process.env.NEXT_PUBLIC_BASE_URL || 'https://faafo-career.com'}/images/logo.png`,
        description: 'Career development platform providing AI-powered assessments and personalized career guidance.',
      },
    ];

    // Inject structured data
    structuredData.forEach((data, index) => {
      const script = document.createElement('script');
      script.type = 'application/ld+json';
      script.id = `structured-data-home-${index}`;
      script.textContent = JSON.stringify(data);

      const existing = document.getElementById(script.id);
      if (existing) existing.remove();

      document.head.appendChild(script);
    });
  }, []);

  return (
    <div className="min-h-screen bg-background">
      <HeroSection
        title="Find Your Path. Build a Fulfilling Career."
        subtitle="Join 10,000+ users taking control of their careers."
        primaryAction={{
          text: "Get Started",
          href: "/signup",
          icon: <Send className="h-5 w-5 transform -rotate-45" />,
        }}
        secondaryAction={{
          text: "Explore Career Paths",
          href: "/career-paths",
        }}
      />

      {/* Features Section */}
      <section className="py-20 bg-gray-50 dark:bg-gray-900 text-foreground px-4">
        <div className="container mx-auto text-center max-w-4xl">
          <h2 className="text-4xl font-bold mb-12">Key Features</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <FeatureCard
              icon={<GraduationCap className="h-12 w-12" />}
              title="Self-Assessment"
              description="Take our comprehensive assessment to discover career paths that match your goals and situation."
              href="/assessment"
            />
            <FeatureCard
              icon={<Briefcase className="h-12 w-12" />}
              title="Career Path Exploration"
              description="Explore detailed career paths with pros, cons, and actionable step-by-step guides."
              href="/career-paths"
            />
            <FeatureCard
              icon={<DollarSign className="h-12 w-12" />}
              title="Freedom Fund Calculator"
              description="Calculate your financial runway and track your progress towards career transition security."
              href="/freedom-fund"
            />
            <FeatureCard
              icon={<BookOpen className="h-12 w-12" />}
              title="Mindset Resources"
              description="Access curated resources to overcome fears, build confidence, and maintain motivation."
              href="/resources"
            />
            <FeatureCard
              icon={<MessageSquare className="h-12 w-12" />}
              title="Community Support"
              description="Connect with others on similar journeys, share experiences, and get peer support."
              href="/forum"
            />
            <div className="md:col-span-2 lg:col-span-1">
              <FeatureCard
                icon={<Lightbulb className="h-12 w-12" />}
                title="AI-Powered Insights"
                description="Get personalized recommendations and insights powered by advanced AI technology."
                href="/assessment"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Additional Features Section */}
      <section className="py-16 bg-gray-50 dark:bg-gray-900 text-foreground px-4">
        <div className="container mx-auto text-center max-w-4xl">
          <h2 className="text-3xl font-bold mb-6">Ready to continue your journey?</h2>
          <p className="text-lg text-gray-600 dark:text-gray-400 mb-8">
            Explore our features to accelerate your career transition.
          </p>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Link href="/dashboard" className="block min-h-[44px]">
              <div className="h-auto p-6 min-h-[44px] flex flex-col gap-2 bg-white dark:bg-gray-800 rounded-lg border hover:shadow-lg transition-shadow">
                <span className="flex justify-center"><GraduationCap className="h-8 w-8" /></span>
                <span className="font-semibold">View Dashboard</span>
                <span className="text-sm opacity-80">See your progress</span>
              </div>
            </Link>
            <Link href="/assessment" className="block min-h-[44px]">
              <div className="h-auto p-6 min-h-[44px] flex flex-col gap-2 bg-white dark:bg-gray-800 rounded-lg border hover:shadow-lg transition-shadow">
                <span className="flex justify-center"><Briefcase className="h-8 w-8" /></span>
                <span className="font-semibold">Take Assessment</span>
                <span className="text-sm opacity-80">Get recommendations</span>
              </div>
            </Link>
            <Link href="/forum" className="block min-h-[44px]">
              <div className="h-auto p-6 min-h-[44px] flex flex-col gap-2 bg-white dark:bg-gray-800 rounded-lg border hover:shadow-lg transition-shadow">
                <span className="flex justify-center"><MessageSquare className="h-8 w-8" /></span>
                <span className="font-semibold">Join Discussion</span>
                <span className="text-sm opacity-80">Connect with community</span>
              </div>
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
}
