'use client';

import React, { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { MessageSquare, Plus, User, Calendar, Filter, Search } from 'lucide-react';
import { Button } from '@/components/ui/button';
import ForumCategories from '@/components/forum/ForumCategories';
import ForumUserProfile from '@/components/forum/ForumUserProfile';
import ReactionButtons from '@/components/forum/ReactionButtons';
import BookmarkButton from '@/components/forum/BookmarkButton';
import ReportButton from '@/components/forum/ReportButton';
import ForumSearch from '@/components/forum/ForumSearch';
import { ListLoadingState, PageErrorState } from '@/components/ui/page-loading-states';
import { ErrorBoundary } from '@/components/ErrorBoundary';
import PageLayout from '@/components/layout/PageLayout';

interface ForumPost {
  id: string;
  title: string;
  content: string;
  createdAt: string;
  isPinned: boolean;
  viewCount: number;
  likeCount: number;
  author: {
    id: string;
    email: string;
    name?: string;
    profile?: {
      profilePictureUrl?: string;
      forumReputation?: number;
      forumPostCount?: number;
      currentCareerPath?: string;
      progressLevel?: string;
    };
  };
  category?: {
    id: string;
    name: string;
    slug: string;
  };
  _count: {
    replies: number;
    reactions: number;
    bookmarks: number;
  };
  reactions: Array<{
    type: string;
    userId: string;
  }>;
}

function ForumPageContent() {
  const { status } = useSession();
  const router = useRouter();
  const [posts, setPosts] = useState<ForumPost[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedCategoryId, setSelectedCategoryId] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [showCategories, setShowCategories] = useState(true);
  const [showSearch, setShowSearch] = useState(false);
  const [categories, setCategories] = useState<Array<{ id: string; name: string; slug: string }>>([]);
  const [postReactions, setPostReactions] = useState<Record<string, any>>({});

  useEffect(() => {
    if (status === 'authenticated') {
      fetchPosts();
      fetchCategories();
    }
  }, [status, selectedCategoryId]);

  // Handle unauthenticated users
  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/login');
    }
  }, [status, router]);

  const fetchPosts = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams();
      if (selectedCategoryId) {
        params.append('categoryId', selectedCategoryId);
      }

      const response = await fetch(`/api/forum/posts?${params.toString()}`);

      if (!response.ok) {
        throw new Error('Failed to fetch posts');
      }

      const data = await response.json();
      const fetchedPosts = data.posts || data;
      setPosts(fetchedPosts);

      // Batch load reactions for all posts to prevent N+1 queries
      if (fetchedPosts.length > 0) {
        await fetchBatchReactions(fetchedPosts.map((post: ForumPost) => post.id));
      }
    } catch (err) {
      console.error('Error fetching posts:', err);
      setError('Failed to load forum posts');
    } finally {
      setLoading(false);
    }
  };

  const fetchBatchReactions = async (postIds: string[]) => {
    try {
      const response = await fetch(`/api/forum/reactions/batch?postIds=${postIds.join(',')}`);
      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setPostReactions(data.data.postReactions);
        }
      }
    } catch (error) {
      console.error('Error fetching batch reactions:', error);
      // Don't fail the whole page if reactions fail to load
    }
  };

  const fetchCategories = async () => {
    try {
      const response = await fetch('/api/forum/categories');
      if (response.ok) {
        const data = await response.json();
        setCategories(data.categories || []);
      }
    } catch (error) {
      console.error('Error fetching categories:', error);
    }
  };

  const handleSearch = async (filters: any) => {
    try {
      setLoading(true);
      const params = new URLSearchParams();

      if (filters.query) params.append('q', filters.query);
      if (filters.category) params.append('category', filters.category);
      if (filters.author) params.append('author', filters.author);
      if (filters.dateRange) params.append('dateRange', filters.dateRange);
      if (filters.sortBy) params.append('sortBy', filters.sortBy);
      if (filters.tags.length > 0) params.append('tags', filters.tags.join(','));

      const response = await fetch(`/api/forum/search?${params.toString()}`);

      if (response.ok) {
        const data = await response.json();
        const searchedPosts = data.posts || [];
        setPosts(searchedPosts);

        // Batch load reactions for search results too
        if (searchedPosts.length > 0) {
          await fetchBatchReactions(searchedPosts.map((post: ForumPost) => post.id));
        }
      }
    } catch (error) {
      console.error('Error searching posts:', error);
      setError('Failed to search posts');
    } finally {
      setLoading(false);
    }
  };

  const handleClearSearch = () => {
    setShowSearch(false);
    fetchPosts();
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getDisplayName = (author: { name?: string; email: string }) => {
    return author.name || author.email.split('@')[0];
  };

  if (status === 'loading' || loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">Community Forum</h1>
          <p className="text-gray-600 dark:text-gray-400">Connect with fellow learners and share your journey</p>
        </div>
        <ListLoadingState itemCount={8} />
      </div>
    );
  }

  if (status === 'unauthenticated') {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <p>Please <Link href="/login" className="text-blue-600 hover:underline">log in</Link> to access the forum.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-7xl">
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold mb-2">Community Forum</h1>
          <p className="text-gray-600 dark:text-gray-400">
            Connect with others on their career transition journey
          </p>
        </div>
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            onClick={() => setShowSearch(!showSearch)}
            className="flex items-center gap-2"
          >
            <Search className="h-4 w-4" />
            {showSearch ? 'Hide Search' : 'Search'}
          </Button>
          <Button
            variant="outline"
            onClick={() => setShowCategories(!showCategories)}
            className="flex items-center gap-2"
          >
            <Filter className="h-4 w-4" />
            {showCategories ? 'Hide Categories' : 'Show Categories'}
          </Button>
          <Button asChild>
            <Link href="/forum/new" className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              New Post
            </Link>
          </Button>
        </div>
      </div>

      {error && (
        <div className="bg-red-100 dark:bg-red-900 text-red-700 dark:text-red-200 p-4 rounded-md mb-6">
          {error}
        </div>
      )}

      {/* Search Component */}
      {showSearch && (
        <ForumSearch
          onSearch={handleSearch}
          onClear={handleClearSearch}
          categories={categories}
          isLoading={loading}
        />
      )}

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
        {/* Categories Sidebar */}
        {showCategories && (
          <div className="lg:col-span-1">
            <ForumCategories
              onCategorySelect={setSelectedCategoryId}
              selectedCategoryId={selectedCategoryId || undefined}
            />
          </div>
        )}

        {/* Main Content */}
        <div className={showCategories ? "lg:col-span-3" : "lg:col-span-4"}>

          <div className="space-y-6">
            {posts.length === 0 ? (
              <div className="text-center py-12">
                <MessageSquare className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                  No posts yet
                </h3>
                <p className="text-gray-600 dark:text-gray-400 mb-4">
                  Be the first to start a conversation in the community!
                </p>
                <Button asChild>
                  <Link href="/forum/new">Create First Post</Link>
                </Button>
              </div>
            ) : (
              posts.map((post) => (
                <div
                  key={post.id}
                  className="bg-white dark:bg-gray-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 hover:shadow-lg transition-shadow"
                >
                  {/* Post Header */}
                  <div className="p-6 border-b border-gray-200 dark:border-gray-700">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          {post.isPinned && (
                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
                              📌 Pinned
                            </span>
                          )}
                          {post.category && (
                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                              {post.category.name}
                            </span>
                          )}
                        </div>
                        <Link
                          href={`/forum/posts/${post.id}`}
                          className="text-xl font-semibold text-gray-900 dark:text-gray-100 hover:text-blue-600 dark:hover:text-blue-400 block"
                        >
                          {post.title}
                        </Link>
                      </div>
                    </div>

                    {/* Author Info */}
                    <ForumUserProfile
                      user={post.author}
                      joinDate={post.createdAt}
                      size="medium"
                    />
                  </div>

                  {/* Post Content */}
                  <div className="p-6">
                    <p className="text-gray-700 dark:text-gray-300 line-clamp-3 mb-4">
                      {post.content.length > 200
                        ? `${post.content.substring(0, 200)}...`
                        : post.content
                      }
                    </p>

                    {/* Post Actions */}
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4">
                        <ReactionButtons
                          postId={post.id}
                          initialReactions={postReactions[post.id]?.reactions?.map((r: any) => ({
                            type: r.type,
                            userId: r.userId,
                            user: { id: r.userId, email: '', name: '' }
                          })) || []}
                          size="small"
                        />
                        <div className="flex items-center gap-1 text-sm text-gray-500 dark:text-gray-400">
                          <MessageSquare className="h-4 w-4" />
                          <span>{post._count.replies} replies</span>
                        </div>
                        <div className="flex items-center gap-1 text-sm text-gray-500 dark:text-gray-400">
                          <Calendar className="h-4 w-4" />
                          <span>{formatDate(post.createdAt)}</span>
                        </div>
                      </div>

                      <div className="flex items-center gap-2">
                        <BookmarkButton
                          postId={post.id}
                          size="small"
                        />
                        <ReportButton
                          postId={post.id}
                          size="small"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

// Wrap the forum page with error boundary
export default function ForumPage() {
  return (
    <ErrorBoundary
      fallback={
        <PageLayout>
          <div className="max-w-4xl mx-auto p-6 text-center">
            <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6">
              <h2 className="text-lg font-semibold text-red-800 dark:text-red-200 mb-2">
                Forum Error
              </h2>
              <p className="text-red-600 dark:text-red-300 mb-4">
                There was an issue loading the forum. Please try refreshing the page.
              </p>
              <button
                onClick={() => window.location.reload()}
                className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
              >
                Refresh Page
              </button>
            </div>
          </div>
        </PageLayout>
      }
      onError={(error, errorInfo) => {
        console.error('Forum Page Error:', { error, errorInfo });
      }}
    >
      <ForumPageContent />
    </ErrorBoundary>
  );
}
